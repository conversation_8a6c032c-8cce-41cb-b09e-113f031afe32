import { AccessTokenValidator } from "@/config/services/access-token-validator";
import { NextRequest, NextResponse } from "next/server";

export async function authMiddleware(request: Readonly<NextRequest>): Promise<NextResponse> {
	const { pathname, searchParams } = request.nextUrl;
	
	// Evita loop infinito verificando se já estamos sendo redirecionados
	if (pathname === "/auth/login") {
		return NextResponse.next();
	}
	
	// Evita redirecionar se já há um parâmetro redirect (pode indicar loop)
	if (searchParams.has('redirect') && searchParams.get('redirect') === pathname) {
		console.warn("Possível loop detectado, permitindo acesso:", pathname);
		return NextResponse.next();
	}
	
	const accessTokenValidator = new AccessTokenValidator();
	const validationResult = await accessTokenValidator.validate(request);
	if (!validationResult.isValid || validationResult.status !== "ok") {
		const keycloakLoginUrl = new URL("/auth/login", request.url);
		keycloakLoginUrl.searchParams.set("redirect", pathname);
		console.log("Redirecting to Keycloak login URL:", keycloakLoginUrl.toString());
	
		return NextResponse.redirect(keycloakLoginUrl, { status: 302 });
	}

	return NextResponse.next();
}
