// import { IKeycloakTokenResponse, IKeycloakUserInfo } from "../interfaces/keycloak-config.interface";
// import { KeycloakConfiguration } from "../config/keycloak.config";
// import { ApiResponse } from "@/shared/types/requests/request.type";

// export interface IKeycloakAuthService {
// 	exchangeCodeForToken(code: string, state: string): Promise<ApiResponse<IKeycloakTokenResponse>>;
// 	getUserInfo(accessToken: string): Promise<ApiResponse<IKeycloakUserInfo>>;
// 	validateState(received: string, expected: string): boolean;
// }

// export class KeycloakAuthService implements IKeycloakAuthService {
// 	private readonly config: KeycloakConfiguration;

// 	constructor(config?: KeycloakConfiguration) {
// 		this.config = config || KeycloakConfiguration.getInstance();
// 	}

// 	async exchangeCodeForToken(code: string, state: string): Promise<ApiResponse<IKeycloakTokenResponse>> {
// 		try {
// 			const { tokenUrl } = this.config.getAuthenticationUrls();
// 			const params = new URLSearchParams({
// 				grant_type: "authorization_code",
// 				client_id: this.config.clientId,
// 				code,
// 				redirect_uri: this.config.redirectUri,
// 			});

// 			const res = await fetch(tokenUrl, {
// 				method: "POST",
// 				headers: {
// 					"Content-Type": "application/x-www-form-urlencoded",
// 					Accept: "application/json",
// 				},
// 				body: params.toString(),
// 			});

// 			if (!res.ok) {
// 				const error = await res.text();
// 				return { success: false, data: { message: `Token exchange error: ${error}` }, status: res.status };
// 			}

// 			const token: IKeycloakTokenResponse = await res.json();
// 			return { success: true, data: token, status: 200 };
// 		} catch (e) {
// 			return {
// 				success: false,
// 				data: { message: `Keycloak communication error: ${e instanceof Error ? e.message : "Unknown error"}` },
// 				status: 500,
// 			};
// 		}
// 	}

// 	async getUserInfo(accessToken: string): Promise<ApiResponse<IKeycloakUserInfo>> {
// 		try {
// 			const { userInfoUrl } = this.config.getAuthenticationUrls();

// 			const res = await fetch(userInfoUrl, {
// 				method: "GET",
// 				headers: {
// 					Authorization: `Bearer ${accessToken}`,
// 					Accept: "application/json",
// 				},
// 			});

// 			if (!res.ok) {
// 				const error = await res.text();
// 				return { success: false, data: { message: `User info error: ${error}` }, status: res.status };
// 			}

// 			const user: IKeycloakUserInfo = await res.json();
// 			return { success: true, data: user, status: 200 };
// 		} catch (e) {
// 			return {
// 				success: false,
// 				data: { message: `User info error: ${e instanceof Error ? e.message : "Unknown error"}` },
// 				status: 500,
// 			};
// 		}
// 	}

// 	validateState(received: string, expected: string): boolean {
// 		return !!received && !!expected && received === expected;
// 	}

// 	generateState(): string {
// 		return crypto.randomUUID();
// 	}

// 	buildAuthorizationUrl(redirectPath?: string): { url: string; state: string } {
// 		const state = this.generateState();
// 		const scope = "openid profile email";
// 		const stateValue = redirectPath ? `${state}:${btoa(redirectPath)}` : state;
// 		const url = this.config.buildAuthorizationUrl(stateValue, scope);
// 		return { url, state: stateValue };
// 	}

// 	getRedirectPathFromState(state: string): string | null {
// 		const parts = state.split(":");
// 		if (parts.length === 2) {
// 			try {
// 				return atob(parts[1]);
// 			} catch {
// 				return null;
// 			}
// 		}
// 		return null;
// 	}
// }
