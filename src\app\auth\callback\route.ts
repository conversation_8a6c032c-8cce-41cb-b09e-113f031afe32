import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest): Promise<NextResponse> {
	try {
		const { searchParams } = new URL(request.url);
		const code = searchParams.get("code");
		const state = searchParams.get("state");
		const error = searchParams.get("error");

        console.log("[Callback]:", { code, state, error });

        return NextResponse.json({            code,
            state,  
            error,
        });

		// Se houve erro no Keycloak
		// if (error) {
		// 	console.error("Erro retornado pelo Keycloak:", error);
		// 	const errorDescription = searchParams.get("error_description");
		// 	return NextResponse.redirect(new URL(`/login?error=${encodeURIComponent(error)}&description=${encodeURIComponent(errorDescription || "")}`, request.url));
		// }

		// // Se não tem code, algo deu errado
		// if (!code) {
		// 	console.error("Código de autorização não encontrado");
		// 	return NextResponse.redirect(new URL("/login?error=no_code", request.url));
		// }

		// // Extrair o path de redirecionamento do state (se existir)
		// let redirectPath = "/";
		// if (state) {
		// 	try {
		// 		// Se o state contém o redirect path codificado
		// 		const parts = state.split(":");
		// 		if (parts.length === 2) {
		// 			redirectPath = atob(parts[1]);
		// 		}
		// 	} catch (e) {
		// 		console.warn("Erro ao decodificar state:", e);
		// 	}
		// }

		// // Enviar o código para o backend processar
		// const backendUrl = process.env.API_ROUTE || "http://**************:10/";
		// const apiUrl = backendUrl.endsWith('/') ? `${backendUrl}auth/callback` : `${backendUrl}/auth/callback`;
		
		// const response = await fetch(apiUrl, {
		// 	method: "POST",
		// 	headers: {
		// 		"Content-Type": "application/json",
		// 	},
		// 	body: JSON.stringify({
		// 		code,
		// 		state,
		// 		// Usar a URL do frontend configurada no .env
		// 		redirect_uri: process.env.NEXT_PUBLIC_APP_URL ? `${process.env.NEXT_PUBLIC_APP_URL}/auth/callback` : `${request.nextUrl.origin}/auth/callback`
		// 	}),
		// });

		// if (!response.ok) {
		// 	console.error("Erro ao processar callback no backend:", response.status);
		// 	return NextResponse.redirect(new URL("/login?error=backend_error", request.url));
		// }

		// const result = await response.json();
		
		// // Se o backend retornou sucesso, redirecionar para a página de destino
		// if (result.success) {
		// 	// O backend deve definir os cookies de autenticação
		// 	const finalRedirect = new URL(redirectPath, request.url);
		// 	const redirectResponse = NextResponse.redirect(finalRedirect);
			
		// 	// Se o backend retornou cookies, definir no frontend
		// 	if (result.tokens) {
		// 		// Definir cookies de autenticação se necessário
		// 		// (isso depende de como seu backend está configurado)
		// 	}
			
		// 	return redirectResponse;
		// } else {
		// 	console.error("Backend retornou erro:", result.error);
		// 	return NextResponse.redirect(new URL(`/login?error=${encodeURIComponent(result.error)}`, request.url));
		// }

	} catch (error) {
		console.error("Erro no processamento do callback:", error);
		return NextResponse.redirect(new URL("/login?error=internal_error", request.url));
	}
}
