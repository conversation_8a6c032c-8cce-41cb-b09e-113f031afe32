"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useSetAtom } from "jotai";
import { userAtom } from "../../atoms/user.atom";
import { isAuthenticatedAtom } from "../../atoms/auth.atom";
import { KeycloakIntegrationService } from "@/config/keycloack";
import { toast } from "@/core/toast";

export interface IUseKeycloakLogoutHook {
	logout: () => void;
	isLoggingOut: boolean;
}

export function useKeycloakLogout(): IUseKeycloakLogoutHook {
	const setUser = useSetAtom(userAtom);
	const setIsAuthenticated = useSetAtom(isAuthenticatedAtom);
	const queryClient = useQueryClient();
	const keycloakIntegration = new KeycloakIntegrationService();

	const mutation = useMutation({
		mutationKey: ["keycloak-logout"],
		mutationFn: async () => {
			const logoutResult = await keycloakIntegration.processLogout(
				process.env.NEXT_PUBLIC_APP_URL
			);
			if (!logoutResult.success) throw new Error("Erro ao processar logout");
			setUser(null);
			setIsAuthenticated(false);
			queryClient.clear();
			window.location.href = logoutResult.data.logoutUrl;
		},
		onError: (error) => {
			console.error("Erro no logout Keycloak:", error);
			toast.error("Erro ao fazer logout");
		},
	});

	return {
		logout: () => mutation.mutate(),
		isLoggingOut: mutation.isPending,
	};
}
