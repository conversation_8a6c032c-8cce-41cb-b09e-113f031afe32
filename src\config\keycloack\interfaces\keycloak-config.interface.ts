export interface IKeycloakConfigurationProvider {
	readonly baseUrl: string;
	readonly realm: string;
	readonly clientId: string;
	readonly redirectUri: string;
}

export interface IKeycloakAuthenticationUrls {
	readonly authorizationUrl: string;
	readonly tokenUrl: string;
	readonly logoutUrl: string;
	readonly userInfoUrl: string;
}

export interface IKeycloakAuthorizationParams {
	readonly clientId: string;
	readonly redirectUri: string;
	readonly responseType: string;
	readonly scope: string;
	readonly state: string;
}

export interface IKeycloakTokenResponse {
	readonly access_token: string;
	readonly token_type: string;
	readonly expires_in: number;
	readonly refresh_token?: string;
	readonly scope: string;
}

export interface IKeycloakUserInfo {
	readonly sub: string;
	readonly name: string;
	readonly preferred_username: string;
	readonly email?: string;
	readonly email_verified?: boolean;
	readonly roles?: string[];
}

export interface IKeycloakCallbackParams {
	readonly code: string;
	readonly state: string;
	readonly sessionState?: string;
}

export interface IKeycloakCallbackError {
	readonly error: string;
	readonly error_description?: string;
	readonly state?: string;
}
