import { COOKIE_NAMES } from "@/config/cookies/name";
import { IRole } from "@/config/enums/role.enum";
import { pathService } from "@/config/path-manager/service";
import { unSignCookie } from "@/shared/lib/cookies/signature/unsign";
import { getCookieName } from "@/shared/lib/cookies/utils/get-cookie-name";
import { getJWTPayloadProperty } from "@/shared/lib/jwt/get-property";
import { NextRequest, NextResponse } from "next/server";

export const permissionsMiddleware = (request: NextRequest): NextResponse => {
	const { pathname } = request.nextUrl;
	if (pathname.includes(".") && !pathname.startsWith("/api/"))return NextResponse.next();
	const accessToken = request.cookies.get(getCookieName(COOKIE_NAMES.ACCESS_TOKEN))?.value;
	if (!accessToken) return NextResponse.rewrite(new URL("/login", request.url));
	const unSignToken = unSignCookie(accessToken);
	const permissions = getJWTPayloadProperty<IRole[]>(unSignToken ?? "", "roles") ?? [];
	const currentRoute = pathService.getItemByPath(pathname);
	const hasPermission = currentRoute ? pathService.hasPermission(currentRoute, permissions) : false;
	if (currentRoute && !currentRoute.route.active) return NextResponse.rewrite(new URL("/404", request.url));
	else if (currentRoute && !hasPermission) return NextResponse.rewrite(new URL("/forbidden", request.url));
	return NextResponse.next();
};

