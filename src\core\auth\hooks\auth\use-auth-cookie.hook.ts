"use client";

import { useCallback, useEffect, useState } from "react";
import { setAuthTokens, getAuthToken, clearAuthTokens, isAuthenticated } from "../../lib/auth-actions";

/**
 * Interface para configuração do hook de autenticação
 */
interface IUseAuthCookieConfig {
	onTokenChange?: (hasToken: boolean) => void;
	onError?: (error: string) => void;
}

/**
 * Interface para resultado de operações de autenticação
 */
interface IAuthCookieResult {
	success: boolean;
	message: string;
}

/**
 * Hook personalizado para gerenciar cookies de autenticação
 * Implementa Single Responsibility Principle (SRP) e Dependency Inversion Principle (DIP)
 */
export function useAuthCookie(config?: IUseAuthCookieConfig) {
	const [isLoading, setIsLoading] = useState<boolean>(false);
	const [hasToken, setHasToken] = useState<boolean | null>(null);
	const [lastError, setLastError] = useState<string | null>(null);

	/**
	 * Verifica se há token válido
	 */
	const checkAuthStatus = useCallback(async (): Promise<boolean> => {
		try {
			setIsLoading(true);
			setLastError(null);

			const authenticated = await isAuthenticated();
			setHasToken(authenticated);

			config?.onTokenChange?.(authenticated);
			return authenticated;
		} catch (error) {
			const errorMessage = `Erro ao verificar status de autenticação: ${error instanceof Error ? error.message : "Erro desconhecido"}`;
			setLastError(errorMessage);
			config?.onError?.(errorMessage);
			return false;
		} finally {
			setIsLoading(false);
		}
	}, [config]);

	/**
	 * Armazena tokens de autenticação
	 */
	const storeTokens = useCallback(
		async (accessToken: string, refreshToken?: string): Promise<IAuthCookieResult> => {
			try {
				setIsLoading(true);
				setLastError(null);

				const result = await setAuthTokens(accessToken, refreshToken);

				if (result.success) {
					setHasToken(true);
					config?.onTokenChange?.(true);
					return {
						success: true,
						message: "Tokens armazenados com sucesso",
					};
				} else {
					const errorMessage = result.data.message || "Erro ao armazenar tokens";
					setLastError(errorMessage);
					config?.onError?.(errorMessage);
					return {
						success: false,
						message: errorMessage,
					};
				}
			} catch (error) {
				const errorMessage = `Erro interno ao armazenar tokens: ${error instanceof Error ? error.message : "Erro desconhecido"}`;
				setLastError(errorMessage);
				config?.onError?.(errorMessage);
				return {
					success: false,
					message: errorMessage,
				};
			} finally {
				setIsLoading(false);
			}
		},
		[config]
	);

	/**
	 * Remove tokens de autenticação
	 */
	const removeTokens = useCallback(async (): Promise<IAuthCookieResult> => {
		try {
			setIsLoading(true);
			setLastError(null);

			const result = await clearAuthTokens();

			if (result.success) {
				setHasToken(false);
				config?.onTokenChange?.(false);
				return {
					success: true,
					message: "Tokens removidos com sucesso",
				};
			} else {
				const errorMessage = result.data.message || "Erro ao remover tokens";
				setLastError(errorMessage);
				config?.onError?.(errorMessage);
				return {
					success: false,
					message: errorMessage,
				};
			}
		} catch (error) {
			const errorMessage = `Erro interno ao remover tokens: ${error instanceof Error ? error.message : "Erro desconhecido"}`;
			setLastError(errorMessage);
			config?.onError?.(errorMessage);
			return {
				success: false,
				message: errorMessage,
			};
		} finally {
			setIsLoading(false);
		}
	}, [config]);

	/**
	 * Obtém o token atual
	 */
	const getCurrentToken = useCallback(async (): Promise<string | null> => {
		try {
			return await getAuthToken();
		} catch (error) {
			const errorMessage = `Erro ao obter token: ${error instanceof Error ? error.message : "Erro desconhecido"}`;
			setLastError(errorMessage);
			config?.onError?.(errorMessage);
			return null;
		}
	}, [config]);

	/**
	 * Limpa erros
	 */
	const clearError = useCallback(() => {
		setLastError(null);
	}, []);

	// Verifica status inicial na montagem do componente
	useEffect(() => {
		checkAuthStatus();
	}, [checkAuthStatus]);

	return {
		// Estado
		isLoading,
		hasToken,
		lastError,

		// Ações
		checkAuthStatus,
		storeTokens,
		removeTokens,
		getCurrentToken,
		clearError,

		// Utilitários
		isAuthenticated: hasToken === true,
	};
}
