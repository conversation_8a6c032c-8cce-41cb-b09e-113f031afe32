// import { IKeycloakConfigurationProvider, IKeycloakAuthenticationUrls } from "../interfaces/keycloak-config.interface";


// export class KeycloakConfiguration implements IKeycloakConfigurationProvider {
// 	private static instance: KeycloakConfiguration;

// 	private constructor(
// 		public readonly baseUrl: string,
// 		public readonly realm: string,
// 		public readonly clientId: string,
// 		public readonly redirectUri: string
// 	) {
// 		this.validateConfiguration();
// 	}


// 	public static getInstance(): KeycloakConfiguration {
// 		if (!KeycloakConfiguration.instance) {
// 			const baseUrl = process.env.KEYCLOAK_BASE_URL;
// 			const realm = process.env.KEYCLOAK_REALM;
// 			const clientId = process.env.KEYCLOAK_CLIENT_ID;
// 			const appUrl = process.env.NEXT_PUBLIC_APP_URL;

// 			if (!baseUrl || !realm || !clientId || !appUrl) {
// 				throw new Error(
// 					"Configuração do Keycloak incompleta. Verifique as variáveis de ambiente: " +
// 						"KEYCLOAK_BASE_URL, KEYCLOAK_REALM, KEYCLOAK_CLIENT_ID, NEXT_PUBLIC_APP_URL"
// 				);
// 			}

// 			const redirectUri = `${appUrl}/auth/callback`;
// 			KeycloakConfiguration.instance = new KeycloakConfiguration(baseUrl, realm, clientId, redirectUri);
// 		}

// 		return KeycloakConfiguration.instance;
// 	}

// 	/**
// 	 * Valida se todas as configurações necessárias estão presentes
// 	 */
// 	private validateConfiguration(): void {
// 		const requiredFields = [
// 			{ field: this.baseUrl, name: "baseUrl" },
// 			{ field: this.realm, name: "realm" },
// 			{ field: this.clientId, name: "clientId" },
// 			{ field: this.redirectUri, name: "redirectUri" },
// 		];

// 		for (const { field, name } of requiredFields) {
// 			if (!field || field.trim() === "") {
// 				throw new Error(`Configuração do Keycloak inválida: ${name} é obrigatório`);
// 			}
// 		}

// 		// Validação de URL
// 		try {
// 			new URL(this.baseUrl);
// 			new URL(this.redirectUri);
// 		} catch {
// 			throw new Error("URLs de configuração do Keycloak são inválidas");
// 		}
// 	}

// 	/**
// 	 * Gera as URLs de autenticação do Keycloak
// 	 */
// 	public getAuthenticationUrls(): IKeycloakAuthenticationUrls {
// 		const realmUrl = `${this.baseUrl}realms/${this.realm}/protocol/openid-connect`;

// 		return {
// 			authorizationUrl: `${realmUrl}/auth`,
// 			tokenUrl: `${realmUrl}/token`,
// 			logoutUrl: `${realmUrl}/logout`,
// 			userInfoUrl: `${realmUrl}/userinfo`,
// 		};
// 	}

// 	/**
// 	 * Gera URL completa de autorização com parâmetros
// 	 */
// 	public buildAuthorizationUrl(state: string, scope: string = "openid profile email"): string {
// 		const urls = this.getAuthenticationUrls();
// 		const params = new URLSearchParams({
// 			client_id: this.clientId,
// 			redirect_uri: this.redirectUri,
// 			response_type: "code",
// 			scope,
// 			state,
// 		});

// 		return `${urls.authorizationUrl}?${params.toString()}`;
// 	}

// 	/**
// 	 * Gera URL de logout
// 	 */
// 	public buildLogoutUrl(redirectUri?: string): string {
// 		const urls = this.getAuthenticationUrls();
// 		const params = new URLSearchParams({
// 			client_id: this.clientId,
// 		});

// 		if (redirectUri) {
// 			params.set("post_logout_redirect_uri", redirectUri);
// 		}

// 		return `${urls.logoutUrl}?${params.toString()}`;
// 	}
// }
