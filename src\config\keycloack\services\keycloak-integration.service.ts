// import { ApiResponse } from "@/shared/types/requests/request.type";
// import { IKeycloakUserInfo } from "../interfaces/keycloak-config.interface";

// import { setAuthTokens, clearAuthTokens } from "@/core/auth/lib/auth-actions";

// /**
//  * Interface para resultado de autenticação integrada
//  */
// export interface IKeycloakIntegratedAuthResult {
// 	success: boolean;
// 	user?: IKeycloakUserInfo;
// 	redirectPath?: string;
// 	error?: string;
// }

// export interface IKeycloakIntegrationService {
// 	processAuthenticationCallback(code: string, state: string, storedState: string): Promise<IKeycloakIntegratedAuthResult>;
// 	generateLoginRedirect(redirectPath?: string): Promise<{ url: string; state: string }>;
// 	processLogout(redirectUri?: string): Promise<ApiResponse<{ logoutUrl: string }>>;
// 	validateAndRefreshToken(currentToken: string): Promise<ApiResponse<boolean>>;
// }

// /**
//  * Serviço de integração entre Keycloak e sistema de autenticação existente
//  * Implementa Open/Closed Principle (OCP) e Dependency Inversion Principle (DIP)
//  */
// export class KeycloakIntegrationService implements IKeycloakIntegrationService {
// 	private readonly keycloakAuth: IKeycloakAuthenticationService;

// 	constructor(keycloakAuth?: IKeycloakAuthenticationService) {
// 		this.keycloakAuth = keycloakAuth || new KeycloakAuthenticationService();
// 	}

// 	/**
// 	 * Processa callback de autenticação completo
// 	 */
// 	public async processAuthenticationCallback(code: string, state: string, storedState: string): Promise<IKeycloakIntegratedAuthResult> {
// 		try {
// 			// Valida estado para prevenir CSRF
// 			if (!this.keycloakAuth.validateState(state, storedState)) {
// 				return {
// 					success: false,
// 					error: "Estado de autenticação inválido",
// 				};
// 			}

// 			// Troca código por token
// 			const tokenResponse = await this.keycloakAuth.exchangeCodeForToken(code, state);
// 			if (!tokenResponse.success) {
// 				return {
// 					success: false,
// 					error: "Erro ao obter token de acesso",
// 				};
// 			}

// 			// Obtém informações do usuário
// 			const userInfoResponse = await this.keycloakAuth.getUserInfo(tokenResponse.data.access_token);
// 			if (!userInfoResponse.success) {
// 				return {
// 					success: false,
// 					error: "Erro ao obter informações do usuário",
// 				};
// 			}

// 			// Armazena token no sistema existente
// 			const setTokenResult = await setAuthTokens(tokenResponse.data.access_token);
// 			if (!setTokenResult.success) {
// 				return {
// 					success: false,
// 					error: "Erro ao armazenar credenciais",
// 				};
// 			}

// 			// Extrai path de redirecionamento
// 			const redirectPath = this.keycloakAuth.extractRedirectFromState(state) || "/";

// 			return {
// 				success: true,
// 				user: userInfoResponse.data,
// 				redirectPath,
// 			};
// 		} catch (error) {
// 			console.error("Erro no processamento de callback:", error);
// 			return {
// 				success: false,
// 				error: "Erro interno no processamento de autenticação",
// 			};
// 		}
// 	}

// 	/**
// 	 * Gera redirecionamento para login
// 	 */
// 	public async generateLoginRedirect(redirectPath?: string): Promise<{ url: string; state: string }> {
// 		return this.keycloakAuth.generateAuthorizationUrl(redirectPath);
// 	}

// 	/**
// 	 * Processa logout integrado
// 	 */
// 	public async processLogout(redirectUri?: string): Promise<ApiResponse<{ logoutUrl: string }>> {
// 		try {
// 			// Limpa tokens do sistema local
// 			const clearResult = await clearAuthTokens();
// 			if (!clearResult.success) {
// 				console.warn("Erro ao limpar tokens locais:", clearResult.data);
// 			}

// 			// Gera URL de logout do Keycloak
// 			const config = this.keycloakAuth["config"] || (this.keycloakAuth as KeycloakAuthenticationService)["config"];

// 			const logoutUrl = config.buildLogoutUrl(redirectUri);

// 			return {
// 				success: true,
// 				data: { logoutUrl },
// 				status: 200,
// 			};
// 		} catch (error) {
// 			return {
// 				success: false,
// 				data: {
// 					message: `Erro no logout: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
// 				},
// 				status: 500,
// 			};
// 		}
// 	}

// 	/**
// 	 * Valida token atual e tenta refresh se necessário
// 	 * Mantém compatibilidade com sistema existente
// 	 */
// 	public async validateAndRefreshToken(currentToken: string): Promise<ApiResponse<boolean>> {
// 		try {
// 			// Tenta obter informações do usuário para validar token
// 			const userInfoResponse = await this.keycloakAuth.getUserInfo(currentToken);

// 			if (userInfoResponse.success) {
// 				return {
// 					success: true,
// 					data: true,
// 					status: 200,
// 				};
// 			}

// 			// Se token inválido, limpa tokens locais
// 			await clearAuthTokens();

// 			return {
// 				success: false,
// 				data: false,
// 				status: 401,
// 			};
// 		} catch (error) {
// 			return {
// 				success: false,
// 				data: false,
// 				status: 500,
// 			};
// 		}
// 	}

// 	/**
// 	 * Verifica se usuário está autenticado via Keycloak
// 	 */
// 	public async isAuthenticated(): Promise<boolean> {
// 		try {
// 			// Implementação pode ser expandida conforme necessário
// 			// Por ora, delega para o sistema existente de validação de tokens
// 			return false; // Será validado pelo middleware existente
// 		} catch {
// 			return false;
// 		}
// 	}
// }
